import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Alert, ScrollView, ActivityIndicator, Modal, Share, Platform } from 'react-native';
import { FIREBASE_AUTH, FIRESTORE_DB } from '../firebaseConfig';
import { getDoc, doc, setDoc, updateDoc, collection, query, where, getDocs } from 'firebase/firestore';
import { format, startOfWeek, endOfWeek, eachDayOfInterval, addWeeks, subWeeks } from 'date-fns';
import XLSX from 'xlsx';
import * as FileSystem from 'expo-file-system';
import * as Sharing from 'expo-sharing';
import { Icon } from 'react-native-elements';

export default function Attendance({ navigation }) {
    const [username, setUsername] = useState('');
    const [checkInTime, setCheckInTime] = useState(null);
    const [checkOutTime, setCheckOutTime] = useState(null);
    const [workedHours, setWorkedHours] = useState(null);
    const [totalWorkedMinutes, setTotalWorkedMinutes] = useState(0);
    const [isCheckInDisabled, setIsCheckInDisabled] = useState(false);
    const [isCheckOutDisabled, setIsCheckOutDisabled] = useState(true);

    // Week and month selection for attendance records
    const currentDate = new Date();
    const [selectedWeek, setSelectedWeek] = useState(currentDate);
    const [selectedMonth, setSelectedMonth] = useState(currentDate);
    const [weeklyAttendance, setWeeklyAttendance] = useState([]);
    const [monthlyAttendance, setMonthlyAttendance] = useState([]);
    const [isLoading, setIsLoading] = useState(false);
    const [leaveDays, setLeaveDays] = useState({});

    // View type and modal state
    const [viewType, setViewType] = useState('weekly');
    const [modalVisible, setModalVisible] = useState(false);

    // Pagination for monthly view
    const [recordsPerPage] = useState(7);
    const [currentPage, setCurrentPage] = useState(1);

    const user = FIREBASE_AUTH.currentUser;

    useEffect(() => {
        const fetchUserData = async () => {
            const user = FIREBASE_AUTH.currentUser;
            if (user) {
                const userDoc = await getDoc(doc(FIRESTORE_DB, 'users', user.uid));
                if (userDoc.exists()) {
                    setUsername(userDoc.data().username);
                } else {
                    setUsername('User');
                }
            } else {
                setUsername('Guest');
            }
        };

        fetchUserData();
    }, []);

    useEffect(() => {
        fetchAttendance();
    }, []);

    // fetch weekly attendance 
    useEffect(() => {
        if (viewType === 'weekly') {
            fetchWeeklyAttendance();
        }
    }, [selectedWeek, viewType]);

    // fetch weekly attendance when component first loads
    useEffect(() => {
        fetchWeeklyAttendance();
    }, []);

    const fetchAttendance = async () => {
        if (!user) return;

        try {
            const todayDate = new Date().toLocaleDateString();

            // document ID includes user ID and date. A unique document for each user for each day created
            const docId = `${user.uid}_${todayDate.replace(/\//g, '-')}`;

            // Reference to today's attendance document
            const docRef = doc(FIRESTORE_DB, 'attendance', docId);
            const docSnap = await getDoc(docRef);

            if (docSnap.exists()) {
                // Document exists for today, use its data
                const data = docSnap.data();
                setCheckInTime(data.checkInTime);
                setCheckOutTime(data.checkOutTime);
                setWorkedHours(data.workedHours);
                setTotalWorkedMinutes(data.totalWorkedMinutes || 0);

                // Disable/Enable Buttons
                if (data.checkInTime) {
                    setIsCheckInDisabled(true);
                    // Always enable check-out if checked in
                    setIsCheckOutDisabled(false);
                } else {
                    setIsCheckInDisabled(false);
                    setIsCheckOutDisabled(true);
                }
            } else {
                // No document exists for today, reset UI
                console.log("No attendance record found for today.");
                setCheckInTime(null);
                setCheckOutTime(null);
                setWorkedHours('');
                setTotalWorkedMinutes(0);
                setIsCheckInDisabled(false);
                setIsCheckOutDisabled(true);
            }
        } catch (error) {
            console.error("Error fetching attendance:", error);
        }
    };

    // Check-In
    const handleCheckIn = async () => {
        if (!user) return;

        try {
            setIsLoading(true);
            const currentTime = new Date();
            const formattedDate = currentTime.toLocaleDateString();
            const docId = `${user.uid}_${formattedDate.replace(/\//g, '-')}`;

            // Check if already checked in today
            const existingDoc = await getDoc(doc(FIRESTORE_DB, 'attendance', docId));
            if (existingDoc.exists()) {
                Alert.alert('Already Checked In', 'You have already checked in today.');
                return;
            }

            // Create new attendance record
            await setDoc(doc(FIRESTORE_DB, 'attendance', docId), {
                userId: user.uid,
                docId: docId,
                checkInTime: currentTime.toISOString(),
                checkOutTime: null,
                workedHours: null,
                totalWorkedMinutes: 0,
                date: formattedDate
            });

            setCheckInTime(currentTime);
            setIsCheckInDisabled(true);
            setIsCheckOutDisabled(false);
            Alert.alert('Success', 'Check-in successful!');
            fetchAttendance();
        } catch (error) {
            console.error('Error during check-in:', error);
            Alert.alert('Error', 'Failed to check in. Please try again.');
        } finally {
            setIsLoading(false);
        }
    };

    // Check-Out
    const handleCheckOut = async () => {
        if (!user || !checkInTime) return;

        try {
            setIsLoading(true);
            const currentTime = new Date();
            const formattedDate = currentTime.toLocaleDateString();
            const docId = `${user.uid}_${formattedDate.replace(/\//g, '-')}`;

            // Get the existing attendance record
            const attendanceDoc = await getDoc(doc(FIRESTORE_DB, 'attendance', docId));
            if (!attendanceDoc.exists()) {
                Alert.alert('Error', 'No check-in record found for today.');
                return;
            }

            const attendanceData = attendanceDoc.data();
            const checkInTime = new Date(attendanceData.checkInTime);
            const timeDiff = currentTime - checkInTime;
            const workedMinutes = Math.floor(timeDiff / (1000 * 60));
            const hours = Math.floor(workedMinutes / 60);
            const minutes = workedMinutes % 60;
            const workedHoursStr = `${hours}h ${minutes}m`;

            // Update the attendance record
            await updateDoc(doc(FIRESTORE_DB, 'attendance', docId), {
                checkOutTime: currentTime.toISOString(),
                workedHours: workedHoursStr,
                totalWorkedMinutes: workedMinutes
            });

            setCheckOutTime(currentTime);
            setWorkedHours(workedHoursStr);
            setTotalWorkedMinutes(workedMinutes);
            setIsCheckOutDisabled(true);
            Alert.alert('Success', 'Check-out successful!');
            fetchAttendance();
        } catch (error) {
            console.error('Error during check-out:', error);
            Alert.alert('Error', 'Failed to check out. Please try again.');
        } finally {
            setIsLoading(false);
        }
    };

    // function to format date strings
    const formatDateTime = (dateTimeString) => {
        if (!dateTimeString) return 'Not Available';

        try {
            const date = new Date(dateTimeString);
            return format(date, 'dd-MM-yyyy HH:mm');
        } catch (error) {
            console.error('Error formatting date:', error);
            return dateTimeString;
        }
    };

    // function to format only time part of date strings
    const formatTimeOnly = (dateTimeString) => {
        if (!dateTimeString) return '-';

        try {
            const date = new Date(dateTimeString);
            return format(date, 'HH:mm');
        } catch (error) {
            console.error('Error formatting time:', error);
            return '-';
        }
    };

    /**
     * Fetches attendance record for a specific date
     * @param {string} date - The date in format MM/DD/YYYY or a Date object
     * @returns {Promise<Object|null>} - The attendance record or null if not found
     */
    
    const fetchAttendanceForDate = async (date) => {
        if (!user) return null;

        try {
            let formattedDate;

            // Handle different date formats
            if (date instanceof Date) {
                // If Date object, convert to local date string
                formattedDate = date.toLocaleDateString();
            }
            else if (typeof date === 'string') {
                // string, use directly
                formattedDate = date;
            }
            else {
                console.error('Invalid date format provided');
                return null;
            }

            // Format the date to match the document ID format
            const docId = `${user.uid}_${formattedDate.replace(/\//g, '-')}`;

            console.log(`Fetching attendance for date: ${formattedDate}, docId: ${docId}`);

            // Reference to the specific attendance document
            const docRef = doc(FIRESTORE_DB, 'attendance', docId);

            // Get the document
            const docSnap = await getDoc(docRef);

            if (docSnap.exists()) {
                // Return the attendance data
                console.log(`Found attendance record for ${formattedDate}`);
                return docSnap.data();
            } else {
                console.log(`No attendance record found for ${formattedDate}`);
                return null;
            }
        } catch (error) {
            console.error('Error fetching attendance record:', error);
            return null;
        }
    };

    // Function to fetch weekly attendance records
    const fetchWeeklyAttendance = async () => {
        if (!user) return;

        setIsLoading(true);
        try {
            // Ensure selectedWeek is a valid Date object
            const weekDate = new Date(selectedWeek);

            // Create date range for the selected week (Sunday to Saturday)
            console.log('Fetching weekly attendance, selectedWeek:', selectedWeek);
            console.log('Converted weekDate:', weekDate);

            // Use consistent options for both start and end of week
            const start = startOfWeek(weekDate, { weekStartsOn: 0 });
            const end = endOfWeek(weekDate, { weekStartsOn: 0 });

            console.log('Week range:', start, 'to', end);

            // Get all days in the week
            const daysInWeek = eachDayOfInterval({ start, end });

            // Initialize attendance data with empty values for all days
            const initialAttendance = daysInWeek.map(day => ({
                date: format(day, 'yyyy-MM-dd'),
                formattedDate: format(day, 'dd-MM'), // day and month
                dayOfWeek: format(day, 'EEE'),
                checkInTime: null,
                checkOutTime: null,
                workedHours: 'N/A',
                totalWorkedMinutes: 0
            }));

            // More efficient approach: Fetch attendance records for each day in the week
            const attendanceData = {};

            // Process each day in the week
            for (const day of daysInWeek) {
                try {
                    // Format the date to match what's stored in Firestore
                    const dateFormatted = day.toLocaleDateString();

                    // Fetch attendance for this specific day
                    const attendanceRecord = await fetchAttendanceForDate(dateFormatted);

                    if (attendanceRecord) {
                        // If record exists, add it to our data
                        const dateKey = format(day, 'yyyy-MM-dd');
                        attendanceData[dateKey] = {
                            checkInTime: attendanceRecord.checkInTime,
                            checkOutTime: attendanceRecord.checkOutTime,
                            workedHours: attendanceRecord.workedHours || 'N/A',
                            totalWorkedMinutes: attendanceRecord.totalWorkedMinutes || 0
                        };
                    }
                } catch (error) {
                    console.error(`Error fetching attendance for ${format(day, 'yyyy-MM-dd')}:`, error);
                }
            }

            // Update the initialAttendance array with actual data
            const updatedAttendance = initialAttendance.map(day => {
                const dateKey = day.date;
                if (attendanceData[dateKey]) {
                    return {
                        ...day,
                        checkInTime: attendanceData[dateKey].checkInTime,
                        checkOutTime: attendanceData[dateKey].checkOutTime,
                        workedHours: attendanceData[dateKey].workedHours,
                        totalWorkedMinutes: attendanceData[dateKey].totalWorkedMinutes
                    };
                }
                return day;
            });

            setWeeklyAttendance(updatedAttendance);
        } catch (error) {
            console.error('Error fetching weekly attendance:', error);
            Alert.alert('Error', 'Failed to load attendance records');
        } finally {
            setIsLoading(false);
        }
    };

    // Function to navigate to previous week
    const goToPreviousWeek = () => {
        setSelectedWeek(prevWeek => subWeeks(prevWeek, 1));
    };

    // Function to navigate to next week
    const goToNextWeek = () => {
        setSelectedWeek(prevWeek => addWeeks(prevWeek, 1));
    };

    // Function to go to current week
    const goToCurrentWeek = () => {
        setSelectedWeek(new Date());
    };

    // Function to fetch monthly attendance records
    const fetchMonthlyAttendance = async () => {
        if (!user) return;

        setIsLoading(true);
        try {
            // Get the year and month from selectedMonth
            const year = selectedMonth.getFullYear();
            const month = selectedMonth.getMonth();

            // Create the first and last day of the month
            const firstDayOfMonth = new Date(year, month, 1);
            const lastDayOfMonth = new Date(year, month + 1, 0);

            console.log(`Fetching monthly attendance for ${year}-${month + 1}`);
            console.log('Month range:', firstDayOfMonth, 'to', lastDayOfMonth);

            // Get all days in the month
            const daysInMonth = [];
            const currentDay = new Date(firstDayOfMonth);

            while (currentDay <= lastDayOfMonth) {
                daysInMonth.push(new Date(currentDay));
                currentDay.setDate(currentDay.getDate() + 1);
            }

            // Initialize attendance data with empty values for all days
            const initialAttendance = daysInMonth.map(day => ({
                date: format(day, 'yyyy-MM-dd'),
                formattedDate: format(day, 'dd-MM'),
                dayOfWeek: format(day, 'EEE'),
                checkInTime: null,
                checkOutTime: null,
                workedHours: 'N/A',
                totalWorkedMinutes: 0
            }));

            // Fetch leave data for the month
            await fetchMonthLeaveData(firstDayOfMonth, lastDayOfMonth);

            // Fetch attendance records for each day in the month
            const attendanceData = {};

            // Process each day in the month
            for (const day of daysInMonth) {
                try {
                    // Format the date to match what's stored in Firestore
                    const dateFormatted = day.toLocaleDateString();

                    // Fetch attendance for this specific day
                    const attendanceRecord = await fetchAttendanceForDate(dateFormatted);

                    if (attendanceRecord) {
                        // If record exists, add it to our data
                        const dateKey = format(day, 'yyyy-MM-dd');
                        attendanceData[dateKey] = {
                            checkInTime: attendanceRecord.checkInTime,
                            checkOutTime: attendanceRecord.checkOutTime,
                            workedHours: attendanceRecord.workedHours || 'N/A',
                            totalWorkedMinutes: attendanceRecord.totalWorkedMinutes || 0
                        };
                    }
                } catch (error) {
                    console.error(`Error fetching attendance for ${format(day, 'yyyy-MM-dd')}:`, error);
                }
            }

            // Update the initialAttendance array with actual data
            const updatedAttendance = initialAttendance.map(day => {
                const dateKey = day.date;
                if (attendanceData[dateKey]) {
                    return {
                        ...day,
                        checkInTime: attendanceData[dateKey].checkInTime,
                        checkOutTime: attendanceData[dateKey].checkOutTime,
                        workedHours: attendanceData[dateKey].workedHours,
                        totalWorkedMinutes: attendanceData[dateKey].totalWorkedMinutes
                    };
                }
                return day;
            });

            setMonthlyAttendance(updatedAttendance);
            // Reset to first page when month changes
            setCurrentPage(1);
        } catch (error) {
            console.error('Error fetching monthly attendance:', error);
            Alert.alert('Error', 'Failed to load monthly attendance records');
        } finally {
            setIsLoading(false);
        }
    };

    // Function to fetch leave data for the entire month
    const fetchMonthLeaveData = async (startDate, endDate) => {
        if (!user) return;

        try {
            console.log('Fetching leave data for month:', format(startDate, 'yyyy-MM-dd'), 'to', format(endDate, 'yyyy-MM-dd'));

            // Query approved leave requests for the current user
            const leaveQuery = query(
                collection(FIRESTORE_DB, 'leaveRequests'),
                where('userId', '==', user.uid),
                where('status', '==', 'Approved')
            );

            const querySnapshot = await getDocs(leaveQuery);
            console.log('Found', querySnapshot.size, 'approved leave requests');

            const leaveData = {};

            // Process each leave request
            querySnapshot.forEach(doc => {
                const leave = doc.data();
                console.log('Processing leave request:', leave);

                // Convert string dates to Date objects
                const leaveStartDate = new Date(leave.startDate);
                const leaveEndDate = new Date(leave.endDate);

                console.log('Leave period:', format(leaveStartDate, 'yyyy-MM-dd'), 'to', format(leaveEndDate, 'yyyy-MM-dd'));

                // Mark all days in the leave period
                const currentDate = new Date(leaveStartDate);
                while (currentDate <= leaveEndDate) {
                    const dateStr = format(currentDate, 'yyyy-MM-dd');

                    // Check if the leave day is within the selected month
                    if (currentDate >= startDate && currentDate <= endDate) {
                        leaveData[dateStr] = true;
                        console.log('Marked leave day:', dateStr);
                    }

                    // Move to next day
                    currentDate.setDate(currentDate.getDate() + 1);
                }
            });

            console.log('Final leave days data for month:', leaveData);
            setLeaveDays(leaveData);
        } catch (error) {
            console.error('Error fetching leave data for month:', error);
        }
    };

    // Function to go to current month
    const goToCurrentMonth = () => {
        setSelectedMonth(new Date());
    };

    // Function to go to previous month
    const goToPreviousMonth = () => {
        setSelectedMonth(prevMonth => {
            const newMonth = new Date(prevMonth);
            newMonth.setMonth(newMonth.getMonth() - 1);
            return newMonth;
        });
    };

    // Function to go to next month
    const goToNextMonth = () => {
        setSelectedMonth(prevMonth => {
            const newMonth = new Date(prevMonth);
            newMonth.setMonth(newMonth.getMonth() + 1);
            return newMonth;
        });
    };

    // Function to export attendance records as Excel file
    const exportAttendanceRecords = async () => {
        try {
            setIsLoading(true);

            // Determine which data to export based on current view
            const dataToExport = viewType === 'weekly' ? weeklyAttendance : monthlyAttendance;

            if (!dataToExport || dataToExport.length === 0) {
                Alert.alert('No Data', 'There are no attendance records to export.');
                setIsLoading(false);
                return;
            }

            // Create worksheet data
            const wsData = [
                ['Day', 'Date', 'Check-In', 'Check-Out', 'Worked Hours'] // Header row
            ];

            // Add data rows
            dataToExport.forEach(record => {
                // Format values for Excel
                const isWeekend = record.dayOfWeek === 'Sat' || record.dayOfWeek === 'Sun';
                const isLeaveDay = leaveDays[record.date] === true;
                const hasCheckedIn = record.checkInTime !== null;
                const hasCheckedOut = record.checkOutTime !== null;

                let checkInValue = hasCheckedIn ? formatTimeOnly(record.checkInTime) :
                                  isLeaveDay ? 'L' : isWeekend ? 'H' : '-';
                let checkOutValue = hasCheckedOut ? formatTimeOnly(record.checkOutTime) :
                                   isLeaveDay ? 'L' : isWeekend ? 'H' : '-';
                let hoursValue = hasCheckedIn && hasCheckedOut ? record.workedHours :
                               isLeaveDay ? 'L' : isWeekend ? 'H' : '-';

                wsData.push([
                    record.dayOfWeek,
                    record.formattedDate,
                    checkInValue,
                    checkOutValue,
                    hoursValue
                ]);
            });

            // Create workbook and worksheet
            const ws = XLSX.utils.aoa_to_sheet(wsData);
            const wb = XLSX.utils.book_new();
            XLSX.utils.book_append_sheet(wb, ws, 'Attendance');

            // Define styles for different row types
            const headerStyle = { fill: { fgColor: { rgb: "007AFF" }, patternType: "solid" }, font: { color: { rgb: "FFFFFF" }, bold: true } };
            const evenRowStyle = { fill: { fgColor: { rgb: "F9F9F9" }, patternType: "solid" } };
            const leaveDayStyle = { fill: { fgColor: { rgb: "FFEBEE" }, patternType: "solid" } };
            const weekendWorkStyle = { fill: { fgColor: { rgb: "E3F2FD" }, patternType: "solid" } };
            const partialDayStyle = { fill: { fgColor: { rgb: "FFF3E0" }, patternType: "solid" } };

            // Apply header style
            const headerRange = XLSX.utils.decode_range(ws['!ref']);
            for (let C = headerRange.s.c; C <= headerRange.e.c; ++C) {
                const cellRef = XLSX.utils.encode_cell({ r: 0, c: C });
                if (!ws[cellRef]) continue;
                if (!ws[cellRef].s) ws[cellRef].s = {};
                Object.assign(ws[cellRef].s, headerStyle);
            }

            // Apply row styles based on conditions
            dataToExport.forEach((record, idx) => {
                const rowIdx = idx + 1; // +1 because header is row 0
                const isWeekend = record.dayOfWeek === 'Sat' || record.dayOfWeek === 'Sun';
                const isLeaveDay = leaveDays[record.date] === true;
                const hasCheckedIn = record.checkInTime !== null;
                const hasCheckedOut = record.checkOutTime !== null;

                // Calculate worked hours for partial day check
                let workedHoursNumeric = 0;
                if (record.totalWorkedMinutes) {
                    workedHoursNumeric = record.totalWorkedMinutes / 60;
                }

                const isPartialDay = hasCheckedIn && hasCheckedOut &&
                                    workedHoursNumeric > 0 &&
                                    workedHoursNumeric <= 4;

                // Determine which style to apply based on priority
                let rowStyle;
                if (isLeaveDay && (hasCheckedIn || hasCheckedOut)) {
                    rowStyle = leaveDayStyle;
                } else if (isWeekend && (hasCheckedIn || hasCheckedOut)) {
                    rowStyle = weekendWorkStyle;
                } else if (!isWeekend && !isLeaveDay && isPartialDay) {
                    rowStyle = partialDayStyle;
                } else if (isLeaveDay) {
                    rowStyle = leaveDayStyle;
                } else if (rowIdx % 2 === 0) {
                    rowStyle = evenRowStyle;
                }

                // Apply the style to each cell in the row
                if (rowStyle) {
                    for (let C = headerRange.s.c; C <= headerRange.e.c; ++C) {
                        const cellRef = XLSX.utils.encode_cell({ r: rowIdx, c: C });
                        if (!ws[cellRef]) ws[cellRef] = { v: "" };
                        if (!ws[cellRef].s) ws[cellRef].s = {};
                        Object.assign(ws[cellRef].s, rowStyle);
                    }
                }
            });

            // Generate Excel file
            const fileType = 'xlsx';
            const fileName = `Attendance_${viewType === 'weekly' ? 'Weekly' : 'Monthly'}_${new Date().getTime()}.${fileType}`;

            // Write the workbook as a base64 string
            const wbout = XLSX.write(wb, { bookType: fileType, type: 'base64' });

            // Create a temporary file path
            const filePath = `${FileSystem.cacheDirectory}${fileName}`;

            // Write the base64 data to a file
            await FileSystem.writeAsStringAsync(filePath, wbout, {
                encoding: FileSystem.EncodingType.Base64
            });

            // Check if sharing is available
            const isSharingAvailable = await Sharing.isAvailableAsync();

            if (isSharingAvailable) {
                // Share the file
                await Sharing.shareAsync(filePath, {
                    mimeType: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                    dialogTitle: 'Export Attendance Records',
                    UTI: 'com.microsoft.excel.xlsx'
                });

                Alert.alert(
                    'Export Successful',
                    'Your attendance records have been exported successfully.'
                );
            } else {
                Alert.alert(
                    'Sharing Not Available',
                    'Sharing is not available on this device.'
                );
            }
        } catch (error) {
            console.error('Error exporting attendance records:', error);
            Alert.alert('Export Failed', 'There was an error exporting the attendance records.');
        } finally {
            setIsLoading(false);
        }
    };

    const handleLogout = () => {
        // Reset all states
        setCheckInTime(null);
        setCheckOutTime(null);
        setWorkedHours('');
        setTotalWorkedMinutes(0);
        setIsCheckInDisabled(false);
        setIsCheckOutDisabled(true);

        FIREBASE_AUTH.signOut()
            .then(() => {
                console.log('User logged out');
                navigation.navigate('Login');
            })
            .catch((error) => {
                console.error('Error logging out:', error);
            });
    };

    return (
        <View style={styles.mainContainer}>
            <View style={styles.container}>
                <View style={styles.header}>
                    <TouchableOpacity onPress={() => navigation.openDrawer()}>
                        <Icon name="menu" size={25} color="#333" />
                    </TouchableOpacity>
                    <Text style={styles.title}>Attendance</Text>
                    <TouchableOpacity onPress={handleLogout}>
                        <Icon name="logout" size={25} color="#333" />
                    </TouchableOpacity>
                </View>

                <ScrollView style={styles.scrollContainer}>
                    {/* Modern attendance card */}
                    <View style={styles.attendanceCard}>
                        <View style={styles.timeStatusItem}>
                            <Icon name="login" type="material-community" size={24} color="#007AFF" />
                            <View style={styles.timeStatusContent}>
                                <View>
                                    <Text style={styles.timeStatusLabel}>Check-In</Text>
                                </View>
                                <Text style={styles.timeStatusValue}>
                                    {checkInTime ? formatDateTime(checkInTime) : 'Not Checked In'}
                                </Text>
                            </View>
                        </View>

                        <View style={styles.timeStatusItem}>
                            <Icon name="logout" type="material-community" size={24} color="#007AFF" />
                            <View style={styles.timeStatusContent}>
                                <View>
                                    <Text style={styles.timeStatusLabel}>Check-Out</Text>
                                </View>
                                <Text style={styles.timeStatusValue}>
                                    {checkOutTime ? formatDateTime(checkOutTime) : 'Not Checked Out'}
                                </Text>
                            </View>
                        </View>

                        {/* Worked hours */}
                        <View style={[styles.timeStatusItem, { marginBottom: 0, paddingBottom: 0, borderBottomWidth: 0 }]}>
                            <Icon name="timer" type="material" size={24} color="#007AFF" />
                            <View style={styles.timeStatusContent}>
                                <View>
                                    <Text style={styles.timeStatusLabel}>Worked Hours</Text>
                                </View>
                                <Text style={styles.timeStatusValue}>
                                    {workedHours ? workedHours : 'N/A'}
                                </Text>
                            </View>
                        </View>
                    </View>

                    {/* Check-in/Check-out buttons */}
                    <View style={styles.buttonRow}>
                        <TouchableOpacity
                            style={[styles.checkinButton, isCheckInDisabled && styles.disabledButton]}
                            onPress={handleCheckIn}
                            disabled={isCheckInDisabled}
                        >
                            <View style={styles.buttonContent}>
                                <Icon name="login" type="material-community" size={20} color="white" />
                                <Text style={styles.buttonText}>Check In</Text>
                            </View>
                        </TouchableOpacity>

                        <TouchableOpacity
                            style={[styles.checkoutButton, isCheckOutDisabled && styles.disabledButton]}
                            onPress={handleCheckOut}
                            disabled={isCheckOutDisabled}
                        >
                            <View style={styles.buttonContent}>
                                <Icon name="logout" type="material-community" size={20} color="white" />
                                <Text style={styles.buttonText}>Check Out</Text>
                            </View>
                        </TouchableOpacity>
                    </View>

                    {/* Attendance Records Section */}
                    <View style={styles.sectionContainer}>
                        <View style={styles.sectionHeader}>
                            <Icon name="calendar" type="material-community" size={24} color="#007AFF" />
                            <TouchableOpacity
                                style={styles.viewTypeButton}
                                onPress={() => setModalVisible(true)}
                            >
                                <Text style={styles.sectionTitle}>
                                    {viewType === 'weekly' ? 'Weekly Attendance Record' : 'Monthly Attendance Record'}
                                </Text>
                                <Icon name="chevron-down" type="material-community" size={20} color="#007AFF" />
                            </TouchableOpacity>
                        </View>

                        {/* View Type Selection Modal */}
                        <Modal
                            animationType="fade"
                            transparent={true}
                            visible={modalVisible}
                            onRequestClose={() => setModalVisible(false)}
                        >
                            <TouchableOpacity
                                style={styles.modalOverlay}
                                activeOpacity={1}
                                onPress={() => setModalVisible(false)}
                            >
                                <View style={styles.modalContent}>
                                    <TouchableOpacity
                                        style={[
                                            styles.modalOption,
                                            viewType === 'weekly' && styles.selectedOption
                                        ]}
                                        onPress={() => {
                                            setViewType('weekly');
                                            setModalVisible(false);
                                        }}
                                    >
                                        <Text style={[
                                            styles.modalOptionText,
                                            viewType === 'weekly' && styles.selectedOptionText
                                        ]}>
                                            Weekly Attendance Record
                                        </Text>
                                    </TouchableOpacity>

                                    <TouchableOpacity
                                        style={[
                                            styles.modalOption,
                                            viewType === 'monthly' && styles.selectedOption
                                        ]}
                                        onPress={() => {
                                            setViewType('monthly');
                                            fetchMonthlyAttendance();
                                            setModalVisible(false);
                                        }}
                                    >
                                        <Text style={[
                                            styles.modalOptionText,
                                            viewType === 'monthly' && styles.selectedOptionText
                                        ]}>
                                            Monthly Attendance Record
                                        </Text>
                                    </TouchableOpacity>
                                </View>
                            </TouchableOpacity>
                        </Modal>

                        {/* Conditional Navigation based on view type */}
                        {viewType === 'weekly' ? (
                            // Weekly View Navigation
                            <View style={styles.weekNavigation}>
                                <TouchableOpacity
                                    style={styles.weekNavButton}
                                    onPress={goToPreviousWeek}
                                >
                                    <Icon name="chevron-left" type="material-community" size={24} color="#007AFF" />
                                </TouchableOpacity>

                                <TouchableOpacity
                                    style={styles.currentWeekButton}
                                    onPress={goToCurrentWeek}
                                >
                                    <Text style={styles.currentWeekText}>
                                        {(() => {
                                            try {
                                                const weekDate = new Date(selectedWeek);
                                                // Ensure valid date
                                                if (isNaN(weekDate.getTime())) {
                                                    return 'Current Week';
                                                }
                                                const start = startOfWeek(weekDate, { weekStartsOn: 0 });
                                                const end = endOfWeek(weekDate, { weekStartsOn: 0 });
                                                return `${format(start, 'dd MMM')} - ${format(end, 'dd MMM yyyy')}`;
                                            } catch (error) {
                                                console.error('Error formatting week dates:', error);
                                                return 'Current Week';
                                            }
                                        })()}
                                    </Text>
                                </TouchableOpacity>

                                <TouchableOpacity
                                    style={styles.weekNavButton}
                                    onPress={goToNextWeek}
                                >
                                    <Icon name="chevron-right" type="material-community" size={24} color="#007AFF" />
                                </TouchableOpacity>
                            </View>
                        ) : (
                            // Monthly View Navigation
                            <View style={styles.weekNavigation}>
                                <TouchableOpacity
                                    style={styles.weekNavButton}
                                    onPress={goToPreviousMonth}
                                >
                                    <Icon name="chevron-left" type="material-community" size={24} color="#007AFF" />
                                </TouchableOpacity>

                                <TouchableOpacity
                                    style={styles.currentWeekButton}
                                    onPress={goToCurrentMonth}
                                >
                                    <Text style={styles.currentWeekText}>
                                        {(() => {
                                            try {
                                                const monthDate = new Date(selectedMonth);
                                                // Ensure valid date
                                                if (isNaN(monthDate.getTime())) {
                                                    return 'Current Month';
                                                }
                                                return format(monthDate, 'MMMM yyyy');
                                            } catch (error) {
                                                console.error('Error formatting month date:', error);
                                                return 'Current Month';
                                            }
                                        })()}
                                    </Text>
                                </TouchableOpacity>

                                <TouchableOpacity
                                    style={styles.weekNavButton}
                                    onPress={goToNextMonth}
                                >
                                    <Icon name="chevron-right" type="material-community" size={24} color="#007AFF" />
                                </TouchableOpacity>
                            </View>
                        )}

                        {/* Attendance Records Table */}
                        {isLoading ? (
                            <ActivityIndicator size="large" color="#007AFF" style={styles.loader} />
                        ) : (
                            <View style={styles.tableContainer}>
                                {/* Table Header */}
                                <View style={styles.tableHeader}>
                                    <Text style={[styles.tableHeaderCell, { flex: 1.5 }]}>Day</Text>
                                    <Text style={[styles.tableHeaderCell, { flex: 1.5 }]}>Date</Text>
                                    <Text style={[styles.tableHeaderCell, { flex: 2 }]}>Check-In</Text>
                                    <Text style={[styles.tableHeaderCell, { flex: 2 }]}>Check-Out</Text>
                                    <Text style={[styles.tableHeaderCell, { flex: 1.5 }]}>Hours</Text>
                                </View>

                                {/* Table Rows - based on view type */}
                                {viewType === 'weekly' ? (

                                    // Weekly Attendance Records
                                    weeklyAttendance.length > 0 ? (
                                    weeklyAttendance.map((record, index) => {

                                    // Check if it's a weekend
                                    const isWeekend = record.dayOfWeek === 'Sat' || record.dayOfWeek === 'Sun';

                                    // Check if it's a leave day
                                    const isLeaveDay = leaveDays[record.date] === true;

                                    // Check if the employee has checked in/out
                                    const hasCheckedIn = record.checkInTime !== null;
                                    const hasCheckedOut = record.checkOutTime !== null;

                                    // Calculate worked hours in numeric form for comparison
                                    let workedHoursNumeric = 0;
                                    if (record.totalWorkedMinutes) {
                                        workedHoursNumeric = record.totalWorkedMinutes / 60;
                                    }

                                    // Check if it's a partial day (≤ 4 hours worked)
                                    const isPartialDay = hasCheckedIn && hasCheckedOut &&
                                                        workedHoursNumeric > 0 &&
                                                        workedHoursNumeric <= 4;

                                    // Determine what to display for check-in
                                    let checkInDisplay;
                                    if (hasCheckedIn) {
                                        // If checked in, always show the time regardless of leave/weekend
                                        checkInDisplay = formatTimeOnly(record.checkInTime);
                                    } else if (isLeaveDay) {
                                        // If it's a leave day without check-in, show "L"
                                        checkInDisplay = 'L';
                                    } else if (isWeekend) {
                                        // If it's a weekend without check-in, show "H"
                                        checkInDisplay = 'H';
                                    } else {
                                        // Regular weekday without check-in
                                        checkInDisplay = '-';
                                    }

                                    // Determine what to display for check-out
                                    let checkOutDisplay;
                                    if (hasCheckedOut) {
                                        // If checked out, always show the time regardless of leave/weekend
                                        checkOutDisplay = formatTimeOnly(record.checkOutTime);
                                    } else if (isLeaveDay) {
                                        // If it's a leave day without check-out, show "L"
                                        checkOutDisplay = 'L';
                                    } else if (isWeekend) {
                                        // If it's a weekend without check-out, show "H"
                                        checkOutDisplay = 'H';
                                    } else {
                                        // Regular weekday without check-out
                                        checkOutDisplay = '-';
                                    }

                                    // Determine what to display for worked hours
                                    let hoursDisplay;
                                    if (hasCheckedIn && hasCheckedOut) {
                                        // If there are worked hours, show them regardless of leave/weekend
                                        hoursDisplay = record.workedHours !== 'N/A' ? record.workedHours : '-';
                                    } else if (isLeaveDay) {
                                        // If it's a leave day without check-in/out, show "L"
                                        hoursDisplay = 'L';
                                    } else if (isWeekend) {
                                        // If it's a weekend without check-in/out, show "H"
                                        hoursDisplay = 'H';
                                    } else {
                                        // Regular weekday without check-in/out
                                        hoursDisplay = '-';
                                    }

                                    // Determine row style based on various conditions
                                    let rowStyle = [styles.tableRow];

                                    // Apply background colors based on priority
                                    if (isLeaveDay && (hasCheckedIn || hasCheckedOut)) {
                                        // Leave day with check-in/out - light red background
                                        rowStyle.push(styles.leaveDayRow);
                                    } else if (isWeekend && (hasCheckedIn || hasCheckedOut)) {
                                        // Weekend with check-in/out - light blue background
                                        rowStyle.push(styles.weekendWorkRow);
                                    } else if (!isWeekend && !isLeaveDay && isPartialDay) {
                                        // Partial day (≤ 4 hours) on regular weekday - light orange background
                                        rowStyle.push(styles.partialDayRow);
                                    } else if (isLeaveDay) {
                                        // Leave day without check-in/out - light red background
                                        rowStyle.push(styles.leaveDayRow);
                                    } else if (index % 2 === 0) {
                                        // Even rows - light gray
                                        rowStyle.push(styles.evenRow);
                                    } else {
                                        // Odd rows - white
                                        rowStyle.push(styles.oddRow);
                                    }

                                    // Determine text style based on the content
                                    const getTextStyle = (content) => {
                                        if (content === 'L' && isLeaveDay) {
                                            return styles.leaveText;
                                        } else if (content === 'H' && isWeekend) {
                                            return styles.weekendText;
                                        } else if (isPartialDay && !isLeaveDay && !isWeekend) {
                                            return styles.partialDayText;
                                        }
                                        return null;
                                    };

                                    return (
                                        <View
                                            key={record.date}
                                            style={rowStyle}
                                        >
                                            <Text style={[styles.tableCell, { flex: 1.5 }]}>{record.dayOfWeek}</Text>
                                            <Text style={[styles.tableCell, { flex: 1.5 }]}>{record.formattedDate}</Text>
                                            <Text style={[styles.tableCell, { flex: 2 }, getTextStyle(checkInDisplay)]}>
                                                {checkInDisplay}
                                            </Text>
                                            <Text style={[styles.tableCell, { flex: 2 }, getTextStyle(checkOutDisplay)]}>
                                                {checkOutDisplay}
                                            </Text>
                                            <Text style={[styles.tableCell, { flex: 1.5 }, getTextStyle(hoursDisplay)]}>
                                                {hoursDisplay}
                                            </Text>
                                        </View>
                                    );
                                    })  
                                ) :  (
                                    <View style={styles.noDataContainer}>
                                        <Text style={styles.noDataText}>No attendance records for the selected week.</Text>
                                    </View>
                                )
                            ) : (
                                // Monthly Attendance Records with Pagination
                                (() => {
                                    // Calculate pagination
                                    const indexOfLastRecord = currentPage * recordsPerPage;
                                    const indexOfFirstRecord = indexOfLastRecord - recordsPerPage;
                                    const currentRecords = monthlyAttendance.slice(indexOfFirstRecord, indexOfFirstRecord + recordsPerPage);
                                    const totalPages = Math.ceil(monthlyAttendance.length / recordsPerPage);
                                    return (
                                        <>
                                            {/* Monthly records */}
                                            {currentRecords.map((record, index) => {
                                                // Check if it's a weekend (Saturday or Sunday)
                                                const isWeekend = record.dayOfWeek === 'Sat' || record.dayOfWeek === 'Sun';
                                                // Check if it's a leave day
                                                // // Special case for April 30th, 2024 which should be marked as a leave day
                                                // let isLeaveDay = leaveDays[record.date] === true;
                                                // if (record.date === '2024-04-30') {
                                                //     isLeaveDay = true;
                                                //     console.log('April 30th leave status:', isLeaveDay, 'Date format:', record.date, 'In leaveDays:', leaveDays[record.date]);
                                                // }
                                                // Check if the employee has checked in/out
                                                const hasCheckedIn = record.checkInTime !== null;
                                                const hasCheckedOut = record.checkOutTime !== null;
                                                // Calculate worked hours in numeric form for comparison
                                                let workedHoursNumeric = 0;
                                                if (record.totalWorkedMinutes) {
                                                    workedHoursNumeric = record.totalWorkedMinutes / 60;
                                                }
                                                // Check if it's a partial day (≤ 4 hours worked)
                                                const isPartialDay = hasCheckedIn && hasCheckedOut &&
                                                                    workedHoursNumeric > 0 &&
                                                                    workedHoursNumeric <= 4;
                                                // Determine what to display for check-in
                                                let checkInDisplay;
                                                if (hasCheckedIn) {
                                                    // If checked in, always show the time regardless of leave/weekend
                                                    checkInDisplay = formatTimeOnly(record.checkInTime);
                                                } else if (isLeaveDay) {
                                                    // If it's a leave day without check-in, show "L"
                                                    checkInDisplay = 'L';
                                                } else if (isWeekend) {
                                                    // If it's a weekend without check-in, show "H"
                                                    checkInDisplay = 'H';
                                                } else {
                                                    // Regular weekday without check-in
                                                    checkInDisplay = '-';
                                                }
                                                // Determine what to display for check-out
                                                let checkOutDisplay;
                                                if (hasCheckedOut) {
                                                    // If checked out, always show the time regardless of leave/weekend
                                                    checkOutDisplay = formatTimeOnly(record.checkOutTime);
                                                } else if (isLeaveDay) {
                                                    // If it's a leave day without check-out, show "L"
                                                    checkOutDisplay = 'L';
                                                } else if (isWeekend) {
                                                    // If it's a weekend without check-out, show "H"
                                                    checkOutDisplay = 'H';
                                                } else {
                                                    // Regular weekday without check-out
                                                    checkOutDisplay = '-';
                                                }
                                                // Determine what to display for worked hours
                                                let hoursDisplay;
                                                if (hasCheckedIn && hasCheckedOut) {
                                                    // If there are worked hours, show them regardless of leave/weekend
                                                    hoursDisplay = record.workedHours !== 'N/A' ? record.workedHours : '-';
                                                } else if (isLeaveDay) {
                                                    // If it's a leave day without check-in/out, show "L"
                                                    hoursDisplay = 'L';
                                                } else if (isWeekend) {
                                                    // If it's a weekend without check-in/out, show "H"
                                                    hoursDisplay = 'H';
                                                } else {
                                                    // Regular weekday without check-in/out
                                                    hoursDisplay = '-';
                                                }
                                                // Determine row style based on various conditions
                                                let rowStyle = [styles.tableRow];
                                                // Apply background colors based on priority
                                                if (isLeaveDay && (hasCheckedIn || hasCheckedOut)) {
                                                    // Leave day with check-in/out - light red background
                                                    rowStyle.push(styles.leaveDayRow);
                                                } else if (isWeekend && (hasCheckedIn || hasCheckedOut)) {
                                                    // Weekend with check-in/out - light blue background
                                                    rowStyle.push(styles.weekendWorkRow);
                                                } else if (!isWeekend && !isLeaveDay && isPartialDay) {
                                                    // Partial day (≤ 4 hours) on regular weekday - light orange background
                                                    rowStyle.push(styles.partialDayRow);
                                                } else if (isLeaveDay) {
                                                    // Leave day without check-in/out - light red background
                                                    rowStyle.push(styles.leaveDayRow);
                                                } else if (index % 2 === 0) {
                                                    // Even rows - light gray
                                                    rowStyle.push(styles.evenRow);
                                                } else {
                                                    // Odd rows - white
                                                    rowStyle.push(styles.oddRow);
                                                }
                                                // Determine text style based on the content
                                                const getTextStyle = (content) => {
                                                    if (content === 'L' && isLeaveDay) {
                                                        return styles.leaveText;
                                                    } else if (content === 'H' && isWeekend) {
                                                        return styles.weekendText;
                                                    } else if (isPartialDay && !isLeaveDay && !isWeekend) {
                                                        return styles.partialDayText;
                                                    }
                                                    return null;
                                                };
                                                // Check if this is the last row to remove bottom border
                                                if (index === currentRecords.length - 1) {
                                                    rowStyle.push({ borderBottomWidth: 0 });
                                                }
                                                return (
                                                    <View
                                                        key={record.date}
                                                        style={rowStyle}
                                                    >
                                                        <Text style={[styles.tableCell, { flex: 1.5 }]}>{record.dayOfWeek}</Text>
                                                        <Text style={[styles.tableCell, { flex: 1.5 }]}>{record.formattedDate}</Text>
                                                        <Text style={[styles.tableCell, { flex: 2 }, getTextStyle(checkInDisplay)]}>
                                                            {checkInDisplay}
                                                        </Text>
                                                        <Text style={[styles.tableCell, { flex: 2 }, getTextStyle(checkOutDisplay)]}>
                                                            {checkOutDisplay}
                                                        </Text>
                                                        <Text style={[styles.tableCell, { flex: 1.5 }, getTextStyle(hoursDisplay)]}>
                                                            {hoursDisplay}
                                                        </Text>
                                                    </View>
                                                );
                                            })}
                                            {/* Pagination Controls */}
                                            <View style={styles.paginationContainer}>
                                                <TouchableOpacity
                                                    style={[styles.paginationButton, currentPage === 1 && styles.disabledButton]}
                                                    onPress={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                                                    disabled={currentPage === 1}
                                                >
                                                    <Text style={styles.paginationButtonText}>Previous</Text>
                                                </TouchableOpacity>
                                                <Text style={styles.paginationText}>
                                                    Page {currentPage} of {totalPages}
                                                </Text>
                                                <TouchableOpacity
                                                    style={[styles.paginationButton, currentPage === totalPages && styles.disabledButton]}
                                                    onPress={() => setCurrentPage(prev =>
                                                        Math.min(totalPages, prev + 1)
                                                    )}
                                                    disabled={currentPage === totalPages}
                                                >
                                                    <Text style={styles.paginationButtonText}>Next</Text>
                                                </TouchableOpacity>
                                            </View>
                                        </>
                                    );
                                })()
                            )}
                            </View>
                        )}

                        {/* Export Button */}
                        <TouchableOpacity
                            style={styles.exportButton}
                            onPress={exportAttendanceRecords}
                        >
                            <View style={styles.buttonContent}>
                                <Icon name="file-export" type="material-community" size={20} color="white" />
                                <Text style={styles.buttonText}>Export Records</Text>
                            </View>
                        </TouchableOpacity>
                    </View>
                </ScrollView>
            </View>
        </View>
    );
}

const styles = StyleSheet.create({
    mainContainer: {
        flex: 1,
        backgroundColor: '#f0f0f0',
    },
    scrollContainer: {
        flex: 1,
        backgroundColor: '#f0f0f0',
    },
    container: {
        flex: 1,
        marginTop: 25,
        alignItems: 'center',
        padding: 16,
        backgroundColor: '#f0f0f0',
    },
    header: {
        width: '100%',
        height: 50,
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        paddingHorizontal: 10,
        borderRadius: 15,
        backgroundColor: '#f8f8f8',
        marginBottom: 15,
    },
    title: {
        fontSize: 19,
        fontWeight: 'bold',
    },

    // Modern attendance card
    attendanceCard: {
        backgroundColor: 'white',
        borderRadius: 15,
        padding: 20,
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 3,
        },
        shadowOpacity: 0.27,
        shadowRadius: 4.65,
        elevation: 6,
        width: '100%',
        marginBottom: 10,
    },
    timeStatusItem: {
        flexDirection: 'row',
        alignItems: 'center',
        marginBottom: 12,
        paddingBottom: 12,
        borderBottomWidth: 1,
        borderBottomColor: '#f0f0f0',
    },
    timeStatusContent: {
        marginLeft: 15,
        flex: 1,
    },
    timeStatusLabel: {
        fontSize: 14,
        color: '#666',
        marginBottom: 0,
    },
    timeStatusValue: {
        fontSize: 16,
        fontWeight: '600',
        color: '#333',
    },

    // Button styles
    buttonRow: {
        flexDirection: 'row',
        justifyContent: 'space-around',
        width: '100%',
        marginBottom: 20,
    },
    checkinButton: {
        backgroundColor: '#007AFF',
        paddingVertical: 12,
        paddingHorizontal: 20,
        borderRadius: 10,
        width: 155,
        shadowColor: '#007AFF',
    },
    checkoutButton: {
        backgroundColor: '#007AFF',
        paddingVertical: 12,
        paddingHorizontal: 20,
        borderRadius: 10,
        width: 155,
    },
    disabledButton: {
        backgroundColor: '#A9A9A9',
        shadowOpacity: 0.1,
    },
    buttonText: {
        color: 'white',
        fontSize: 15,
        fontWeight: 'bold',
        marginLeft: 8,
    },
    buttonContent: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
    },

    // Weekly Attendance Records Section
    sectionContainer: {
        backgroundColor: 'white',
        borderRadius: 15,
        padding: 20,
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 3,
        },
        shadowOpacity: 0.27,
        shadowRadius: 4.65,
        elevation: 6,
        width: '100%',
        marginTop: -5,
        marginBottom: 20,
    },
    sectionHeader: {
        flexDirection: 'row',
        alignItems: 'center',
        marginBottom: 15,
    },
    sectionTitle: {
        fontSize: 16,
        fontWeight: 'bold',
        color: '#333',
        marginLeft: 10,
    },
    weekNavigation: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: 15,
    },
    weekNavButton: {
        padding: 5,
        borderRadius: 5,
        backgroundColor: '#f0f0f0',
    },
    currentWeekButton: {
        padding: 8,
        borderRadius: 8,
        backgroundColor: '#f0f0f0',
        flex: 1,
        marginHorizontal: 10,
        alignItems: 'center',
    },
    currentWeekText: {
        fontSize: 14,
        fontWeight: '600',
        color: '#007AFF',
    },

    // Table styles
    tableContainer: {
        borderWidth: 1,
        borderColor: '#ddd',
        borderRadius: 8,
        overflow: 'hidden',
        marginBottom: 15,
        marginLeft: -5,
        marginRight: -5,
    },
    tableHeader: {
        flexDirection: 'row',
        backgroundColor: '#007AFF',
        padding: 10,
    },
    tableHeaderCell: {
        color: 'white',
        fontWeight: 'bold',
        fontSize: 13,
        textAlign: 'center',
    },
    tableRow: {
        flexDirection: 'row',
        borderBottomWidth: 1,
        borderBottomColor: '#ddd',
        padding: 5,
    },
    evenRow: {
        backgroundColor: '#f9f9f9',
    },
    oddRow: {
        backgroundColor: 'white',
    },
    leaveDayRow: {
        backgroundColor: '#FFEBEE',
    },
    leaveText: {
        color: '#F44336',
        fontWeight: 'bold',
    },
    weekendWorkRow: {
        backgroundColor: '#E3F2FD',
    },
    weekendText: {
        color: '#007AFF',
        fontWeight: 'bold',
    },
    partialDayRow: {
        backgroundColor: '#FFF3E0',
    },
    partialDayText: {
        color: '#FF9800',
        fontWeight: 'bold',
    },
    tableCell: {
        fontSize: 13,
        textAlign: 'center',
    },

    // Export button
    exportButton: {
        backgroundColor: '#007AFF',
        paddingVertical: 12,
        paddingHorizontal: 20,
        borderRadius: 10,
        alignSelf: 'center',
        width: '60%',
    },

    // Loading indicator
    loader: {
        marginVertical: 20,
    },

    // View type button
    viewTypeButton: {
        flexDirection: 'row',
        alignItems: 'justify-center',
        marginLeft: 10,
        paddingVertical: 5,
        paddingHorizontal: 5,
        borderRadius: 5,
        backgroundColor: '#f0f0f0',
    },

    // Modal styles
    modalOverlay: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: 'rgba(0, 0, 0, 0.5)',
    },
    modalContent: {
        width: '80%',
        backgroundColor: 'white',
        borderRadius: 10,
        padding: 20,
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 2,
        },
        shadowOpacity: 0.25,
        shadowRadius: 3.84,
        elevation: 5,
    },
    modalOption: {
        paddingVertical: 15,
        paddingHorizontal: 10,
        borderBottomWidth: 1,
        borderBottomColor: '#f0f0f0',
    },
    selectedOption: {
        backgroundColor: '#E3F2FD',
    },
    modalOptionText: {
        fontSize: 16,
        color: '#333',
    },
    selectedOptionText: {
        color: '#007AFF',
        fontWeight: 'bold',
    },

    // Pagination styles
    paginationContainer: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        // marginTop: 10,
        paddingTop: 5,
        borderTopWidth: 1,
        borderTopColor: '#ddd',
    },
    paginationButton: {
        backgroundColor: '#007AFF',
        paddingVertical: 8,
        paddingHorizontal: 15,
        borderRadius: 5,
    },
    paginationButtonText: {
        color: 'white',
        fontWeight: 'bold',
        marginLeft: 5,
    },
    paginationText: {
        fontSize: 14,
        color: '#666',
    },

    // No data styles
    noDataContainer: {
        padding: 20,
        alignItems: 'center',
        justifyContent: 'center',
        backgroundColor: '#f9f9f9',
        borderRadius: 8,
        marginVertical: 10,
    },
    noDataText: {
        fontSize: 16,
        color: '#666',
        textAlign: 'center',
        fontStyle: 'italic',
    },
});
